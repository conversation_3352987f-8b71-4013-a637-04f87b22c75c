<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flare Portal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: white;
            border-bottom: 1px solid #e9ecef;
            padding: 0 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1200px;
            margin: 0 auto;
            height: 70px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: #333;
            text-decoration: none;
        }

        .logo::before {
            content: "⚡";
            margin-right: 0.5rem;
            font-size: 1.8rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
            align-items: center;
        }

        .nav-item {
            position: relative;
        }

        .nav-link {
            text-decoration: none;
            color: #666;
            font-weight: 500;
            padding: 0.5rem 0;
            transition: color 0.2s;
        }

        .nav-link:hover {
            color: #333;
        }

        .nav-link.active {
            color: #333;
            border-bottom: 2px solid #333;
        }

        .flare-dropdown {
            display: flex;
            align-items: center;
            background: #e91e63;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.2s;
        }

        .flare-dropdown:hover {
            background: #c2185b;
        }

        .flare-dropdown::after {
            content: "▼";
            margin-left: 0.5rem;
            font-size: 0.8rem;
        }

        .connect-wallet {
            background: transparent;
            border: 1px solid #ddd;
            padding: 0.5rem 1.5rem;
            border-radius: 20px;
            color: #666;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }

        .connect-wallet:hover {
            border-color: #333;
            color: #333;
        }

        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 4rem 2rem;
            flex: 1;
        }

        .welcome-section {
            text-align: center;
            margin-bottom: 3rem;
        }

        .welcome-title {
            font-size: 2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 2rem;
        }

        .info-card {
            background: #2c3e50;
            color: white;
            padding: 2rem;
            border-radius: 12px;
            max-width: 800px;
            margin: 0 auto;
            text-align: left;
            line-height: 1.7;
        }

        .info-card p {
            margin-bottom: 1.5rem;
        }

        .info-card a {
            color: #e91e63;
            text-decoration: none;
        }

        .info-card a:hover {
            text-decoration: underline;
        }

        .footer {
            margin-top: 6rem;
            padding: 2rem;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
        }

        .footer-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .footer-links {
            display: flex;
            gap: 1rem;
        }

        .footer-links a {
            color: #666;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .footer-links a:hover {
            color: #333;
        }

        .social-icons {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .social-icon {
            width: 24px;
            height: 24px;
            background: #666;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-decoration: none;
            font-size: 0.8rem;
            transition: background-color 0.2s;
        }

        .social-icon:hover {
            background: #333;
        }

        @media (max-width: 768px) {
            .nav-container {
                flex-direction: column;
                height: auto;
                padding: 1rem 0;
            }

            .nav-menu {
                flex-wrap: wrap;
                gap: 1rem;
                margin-top: 1rem;
            }

            .main-content {
                padding: 2rem 1rem;
            }

            .welcome-title {
                font-size: 1.5rem;
            }

            .info-card {
                padding: 1.5rem;
            }

            .footer-content {
                flex-direction: column;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav-container">
            <a href="#" class="logo">flare</a>

            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="#" class="nav-link active">Account</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">Staking</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">USDTO</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">Voting</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link">Management</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="flare-dropdown">🔥 Flare</a>
                </li>
                <li class="nav-item">
                    <button type="button" class="connect-wallet">Connect to Wallet</button>
                </li>
            </ul>
        </nav>
    </header>

    <main class="main-content">
        <section class="welcome-section">
            <h1 class="welcome-title">Welcome to the Flare portal!</h1>

            <div class="info-card">
                <p>Here, you can manage both Songbird (SGB) and Flare (FLR) network tokens, wrap and delegate them to FTSO data providers and thereby contribute to the networks' decentralization and stability. You can also participate in governance voting from this portal.</p>

                <p>Please start by clicking on the 'Connect to Wallet' button, select your wallet and follow the instructions. This page is compatible for both desktop and mobile browsers. For technical support, please make a request in the General room on our <a href="#">discord</a> or on our <a href="#">telegram</a>.</p>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-links">
                <span>© 2025 Flare Network</span>
                <a href="#">Terms & Privacy</a>
            </div>

            <div class="social-icons">
                <a href="#" class="social-icon" title="Twitter">🐦</a>
                <a href="#" class="social-icon" title="Discord">💬</a>
                <a href="#" class="social-icon" title="Telegram">📱</a>
                <a href="#" class="social-icon" title="GitHub">🐙</a>
                <a href="#" class="social-icon" title="YouTube">📺</a>
                <a href="#" class="social-icon" title="Medium">📝</a>
                <a href="#" class="social-icon" title="Instagram">📷</a>
            </div>
        </div>
    </footer>

    <script>
        // Connect wallet functionality
        document.querySelector('.connect-wallet').addEventListener('click', function() {
            alert('Wallet connection functionality would be implemented here');
        });

        // Navigation functionality
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Remove active class from all links
                document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));

                // Add active class to clicked link
                this.classList.add('active');
            });
        });

        // Flare dropdown functionality
        document.querySelector('.flare-dropdown').addEventListener('click', function(e) {
            e.preventDefault();
            alert('Flare network selection dropdown would be implemented here');
        });
    </script>
</body>
</html>