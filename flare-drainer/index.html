<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flare Portal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'flare-pink': '#e91e63',
                        'flare-pink-dark': '#c2185b',
                        'slate-dark': '#2c3e50'
                    }
                }
            }
        }
    </script>
</head>
<body class="min-h-screen flex flex-col bg-gray-50 text-gray-800 font-sans">
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 sticky top-0 z-50">
        <nav class="max-w-6xl mx-auto px-8 h-[70px] flex items-center justify-between">
            <!-- Logo -->
            <a href="#" class="flex items-center text-2xl font-bold text-gray-800 no-underline">
                <span class="text-3xl mr-2">⚡</span>
                flare
            </a>
            
            <!-- Navigation Menu -->
            <ul class="hidden md:flex items-center space-x-8">
                <li><a href="#" class="nav-link active text-gray-600 font-medium py-2 hover:text-gray-800 transition-colors border-b-2 border-gray-800">Account</a></li>
                <li><a href="#" class="nav-link text-gray-600 font-medium py-2 hover:text-gray-800 transition-colors">Staking</a></li>
                <li><a href="#" class="nav-link text-gray-600 font-medium py-2 hover:text-gray-800 transition-colors">USDTO</a></li>
                <li><a href="#" class="nav-link text-gray-600 font-medium py-2 hover:text-gray-800 transition-colors">Voting</a></li>
                <li><a href="#" class="nav-link text-gray-600 font-medium py-2 hover:text-gray-800 transition-colors">Management</a></li>
                <li>
                    <a href="#" class="flare-dropdown flex items-center bg-flare-pink text-white px-4 py-2 rounded-full font-medium hover:bg-flare-pink-dark transition-colors">
                        🔥 Flare
                        <span class="ml-2 text-xs">▼</span>
                    </a>
                </li>
                <li>
                    <button type="button" class="connect-wallet bg-transparent border border-gray-300 px-6 py-2 rounded-full text-gray-600 font-medium hover:border-gray-800 hover:text-gray-800 transition-all cursor-pointer">
                        Connect to Wallet
                    </button>
                </li>
            </ul>

            <!-- Mobile Menu Button -->
            <button class="md:hidden p-2" id="mobile-menu-btn">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>
        </nav>

        <!-- Mobile Menu -->
        <div class="md:hidden hidden" id="mobile-menu">
            <div class="px-8 py-4 space-y-4 border-t border-gray-200">
                <a href="#" class="block text-gray-600 font-medium py-2">Account</a>
                <a href="#" class="block text-gray-600 font-medium py-2">Staking</a>
                <a href="#" class="block text-gray-600 font-medium py-2">USDTO</a>
                <a href="#" class="block text-gray-600 font-medium py-2">Voting</a>
                <a href="#" class="block text-gray-600 font-medium py-2">Management</a>
                <a href="#" class="block bg-flare-pink text-white px-4 py-2 rounded-full font-medium text-center">🔥 Flare</a>
                <button type="button" class="block w-full border border-gray-300 px-6 py-2 rounded-full text-gray-600 font-medium">Connect to Wallet</button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="flex-1 max-w-6xl mx-auto px-8 py-16">
        <section class="text-center mb-12">
            <h1 class="text-3xl md:text-4xl font-semibold text-gray-800 mb-8">Welcome to the Flare portal!</h1>
            
            <div class="bg-slate-dark text-white p-8 rounded-xl max-w-4xl mx-auto text-left leading-relaxed">
                <p class="mb-6">
                    Here, you can manage both Songbird (SGB) and Flare (FLR) network tokens, wrap and delegate them to FTSO data providers 
                    and thereby contribute to the networks' decentralization and stability. You can also participate in governance voting from this portal.
                </p>
                
                <p>
                    Please start by clicking on the 'Connect to Wallet' button, select your wallet and follow the instructions. This page is compatible 
                    for both desktop and mobile browsers. For technical support, please make a request in the General room on our 
                    <a href="#" class="text-flare-pink hover:underline">discord</a> or on our 
                    <a href="#" class="text-flare-pink hover:underline">telegram</a>.
                </p>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="mt-auto bg-white border-t border-gray-200 py-8">
        <div class="max-w-6xl mx-auto px-8">
            <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                    <span>© 2025 Flare Network</span>
                    <a href="#" class="hover:text-gray-800 transition-colors">Terms & Privacy</a>
                </div>
                
                <div class="flex items-center space-x-4">
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="Twitter">🐦</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="Discord">💬</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="Telegram">📱</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="GitHub">🐙</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="YouTube">📺</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="Medium">📝</a>
                    <a href="#" class="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center text-white text-xs hover:bg-gray-800 transition-colors" title="Instagram">📷</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Connect wallet functionality
        document.querySelectorAll('.connect-wallet').forEach(btn => {
            btn.addEventListener('click', function() {
                alert('Wallet connection functionality would be implemented here');
            });
        });

        // Navigation functionality
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all links
                document.querySelectorAll('.nav-link').forEach(l => {
                    l.classList.remove('border-b-2', 'border-gray-800');
                });
                
                // Add active class to clicked link
                this.classList.add('border-b-2', 'border-gray-800');
            });
        });

        // Flare dropdown functionality
        document.querySelectorAll('.flare-dropdown').forEach(dropdown => {
            dropdown.addEventListener('click', function(e) {
                e.preventDefault();
                alert('Flare network selection dropdown would be implemented here');
            });
        });
    </script>
</body>
</html>
